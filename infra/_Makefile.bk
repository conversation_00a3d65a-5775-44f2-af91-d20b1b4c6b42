# Makefile
# Usage: make <target>

# =========================
# 🔧 Variables
# =========================
DOCKER_COMPOSE := docker compose
COMPOSE_FILE := docker-compose.yml
PROJECT_NAME := msarknet
NETWORK_NAME := proxy
CERT_DOMAIN := msarknet.me
SERVICES := traefik main-app grafana prometheus whoami portainer docs

##@ 📚 Información y Ayuda


##@ 🎯 Atajos Rápidos

# .PHONY: dev
# dev: up logs-traefik ## 🚀 Modo desarrollo (up + logs de Traefik)

# .PHONY: quick-test
# quick-test: ## ⚡ Test rápido de funcionamiento
# 	@curl -k -s https://localhost/ | grep -q "MSarkNet" && echo -e "$(GREEN)✅ Test OK$(NC)" || echo -e "$(RED)❌ Test Failed$(NC)"
