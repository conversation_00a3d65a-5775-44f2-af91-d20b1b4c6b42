{"name": "split-ca", "version": "1.0.1", "description": "Simple module to split a single certificate authority chain file (aka: bundle, ca-bundle, ca-chain, etc.) into an array, as expected by the node.js tls api.", "main": "index.js", "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "https://github.com/bushong1/split-ca.git"}, "keywords": ["nodejs", "ca", "chain", "ssl", "tls", "https", "certificate", "authority", "bundle", "ca-bundle", "ca-chain", "split", "server"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/bushong1/split-ca/issues"}, "homepage": "https://github.com/bushong1/split-ca", "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0"}}