{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "name": "touch", "description": "like touch(1) in node", "version": "3.1.1", "repository": "git://github.com/isaacs/node-touch.git", "bin": {"nodetouch": "./bin/nodetouch.js"}, "license": "ISC", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "files": ["index.js", "bin/nodetouch.js"]}