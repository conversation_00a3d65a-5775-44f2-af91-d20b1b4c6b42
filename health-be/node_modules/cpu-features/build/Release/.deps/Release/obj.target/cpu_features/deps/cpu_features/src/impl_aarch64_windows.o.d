cmd_Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_windows.o := cc -o Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_windows.o ../deps/cpu_features/src/impl_aarch64_windows.c '-DNODE_GYP_MODULE_NAME=cpu_features' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DNDEBUG' '-DSTACK_LINE_READER_BUFFER_SIZE=1024' -I/home/<USER>/.cache/node-gyp/22.15.0/include/node -I/home/<USER>/.cache/node-gyp/22.15.0/src -I/home/<USER>/.cache/node-gyp/22.15.0/deps/openssl/config -I/home/<USER>/.cache/node-gyp/22.15.0/deps/openssl/openssl/include -I/home/<USER>/.cache/node-gyp/22.15.0/deps/uv/include -I/home/<USER>/.cache/node-gyp/22.15.0/deps/zlib -I/home/<USER>/.cache/node-gyp/22.15.0/deps/v8/include -I../deps/cpu_features/include -I../deps/cpu_features/include/internal  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -O3 -m64 -O3 -fno-omit-frame-pointer  -MMD -MF ./Release/.deps/Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_windows.o.d.raw   -c
Release/obj.target/cpu_features/deps/cpu_features/src/impl_aarch64_windows.o: \
 ../deps/cpu_features/src/impl_aarch64_windows.c \
 ../deps/cpu_features/include/cpu_features_macros.h
../deps/cpu_features/src/impl_aarch64_windows.c:
../deps/cpu_features/include/cpu_features_macros.h:
