name: amd64 MacOS CMake

on:
  push:
  pull_request:
  schedule:
    # min hours day(month) month day(week)
    - cron: '0 0 7,22 * *'

jobs:
  # Building using the github runner environement directly.
  xcode:
    runs-on: macos-latest
    env:
      CTEST_OUTPUT_ON_FAILURE: 1
    steps:
    - uses: actions/checkout@v2
    - name: Check cmake
      run: cmake --version
    - name: Configure
      run: cmake -S. -Bbuild -G "Xcode" -DCMAKE_CONFIGURATION_TYPES=Release
    - name: Build
      run: cmake --build build --config Release --target ALL_BUILD -v
    - name: Test
      run: cmake --build build --config Release --target RUN_TESTS -v
    - name: Install
      run: cmake --build build --config Release --target install -v
  make:
    runs-on: macos-latest
    env:
      CTEST_OUTPUT_ON_FAILURE: 1
    steps:
    - uses: actions/checkout@v2
    - name: Check cmake
      run: cmake --version
    - name: Configure
      run: cmake -S. -Bbuild -DCMAKE_BUILD_TYPE=Release
    - name: Build
      run: cmake --build build --target all -v
    - name: Test
      run: cmake --build build --target test -v
    - name: Install
      run: cmake --build build --target install -v
