name: amd64 Linux Bazel

on:
  push:
  pull_request:
  schedule:
    # min hours day(month) month day(week)
    - cron: '0 0 7,22 * *'

jobs:
  # Building using the github runner environement directly.
  bazel:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v2
      - name: Install Bazel
        run: |
          curl -fsSL https://bazel.build/bazel-release.pub.gpg | gpg --dearmor > bazel.gpg
          sudo mv bazel.gpg /etc/apt/trusted.gpg.d/
          echo "deb [arch=amd64] https://storage.googleapis.com/bazel-apt stable jdk1.8" | sudo tee /etc/apt/sources.list.d/bazel.list
          sudo apt-get update
          sudo apt-get install bazel
          bazel --version
      - name: Test
        run: bazel test -s --verbose_failures //...
