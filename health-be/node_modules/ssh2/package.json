{"name": "ssh2", "version": "1.17.0", "author": "<PERSON> <<EMAIL>>", "description": "SSH2 client and server modules written in pure JavaScript for node.js", "main": "./lib/index.js", "engines": {"node": ">=10.16.0"}, "dependencies": {"asn1": "^0.2.6", "bcrypt-pbkdf": "^1.0.2"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "optionalDependencies": {"cpu-features": "~0.0.10", "nan": "^2.23.0"}, "scripts": {"install": "node install.js", "rebuild": "node install.js", "test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js examples lib test", "lint:fix": "npm run lint -- --fix"}, "keywords": ["ssh", "ssh2", "sftp", "secure", "shell", "exec", "remote", "client"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/ssh2/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/ssh2.git"}}