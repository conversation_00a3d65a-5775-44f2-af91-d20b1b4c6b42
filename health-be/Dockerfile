# ================================
# Dockerfile para Node.js Backend
# Ubicación: cerebro-be/Dockerfile
# ================================

# Stage base - Node.js
FROM node:18-alpine AS base
WORKDIR /app

# Instalar dependencias del sistema
RUN apk add --no-cache \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S nodejs -u 1001

# Copiar archivos de dependencias
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Stage de desarrollo
FROM node:18-alpine AS development
WORKDIR /app

# Instalar nodemon globalmente para hot reload
RUN npm install -g nodemon

# Copiar archivos de dependencias
COPY package*.json ./
RUN npm install

# Copiar código fuente
COPY . .

# Variables de entorno para desarrollo
ENV NODE_ENV=development
ENV PORT=5001

# Exponer puerto
EXPOSE 5001

# Comando de desarrollo con nodemon para hot reload
CMD ["npm", "run", "dev"]

# Stage de construcción (si tienes TypeScript)
FROM node:18-alpine AS build
WORKDIR /app

# Copiar dependencias de desarrollo
COPY package*.json ./
RUN npm install

# Copiar código fuente
COPY . .

# Construir aplicación (si usas TypeScript)
RUN npm run build

# Stage de producción
FROM node:18-alpine AS production
WORKDIR /app

# Crear usuario no-root
RUN addgroup -g 1001 -S nodejs \
    && adduser -S nodejs -u 1001

# Copiar dependencias de producción
COPY --from=base /app/node_modules ./node_modules
COPY package*.json ./

# Copiar código fuente (no hay build para JS puro)
COPY src ./src

# Cambiar ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

# Variables de entorno para producción
ENV NODE_ENV=production
ENV PORT=5001

# Exponer puerto
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5001/api/health || exit 1

# Comando para producción
CMD ["dumb-init", "node", "src/index.js"]

# Stage por defecto (desarrollo)
FROM development
